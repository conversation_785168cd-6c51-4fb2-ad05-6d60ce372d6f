{"ast": null, "code": "'use strict';\n\nvar IS_PURE = require('../internals/is-pure');\nvar globalThis = require('../internals/global-this');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar SHARED = '__core-js_shared__';\nvar store = module.exports = globalThis[SHARED] || defineGlobalProperty(SHARED, {});\n(store.versions || (store.versions = [])).push({\n  version: '3.45.1',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2025 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.45.1/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});", "map": {"version": 3, "names": ["IS_PURE", "require", "globalThis", "defineGlobalProperty", "SHARED", "store", "module", "exports", "versions", "push", "version", "mode", "copyright", "license", "source"], "sources": ["D:/leadcdn/node_modules/core-js-pure/internals/shared-store.js"], "sourcesContent": ["'use strict';\nvar IS_PURE = require('../internals/is-pure');\nvar globalThis = require('../internals/global-this');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = module.exports = globalThis[SHARED] || defineGlobalProperty(SHARED, {});\n\n(store.versions || (store.versions = [])).push({\n  version: '3.45.1',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2025 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.45.1/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIC,UAAU,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,IAAIE,oBAAoB,GAAGF,OAAO,CAAC,qCAAqC,CAAC;AAEzE,IAAIG,MAAM,GAAG,oBAAoB;AACjC,IAAIC,KAAK,GAAGC,MAAM,CAACC,OAAO,GAAGL,UAAU,CAACE,MAAM,CAAC,IAAID,oBAAoB,CAACC,MAAM,EAAE,CAAC,CAAC,CAAC;AAEnF,CAACC,KAAK,CAACG,QAAQ,KAAKH,KAAK,CAACG,QAAQ,GAAG,EAAE,CAAC,EAAEC,IAAI,CAAC;EAC7CC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAEX,OAAO,GAAG,MAAM,GAAG,QAAQ;EACjCY,SAAS,EAAE,2CAA2C;EACtDC,OAAO,EAAE,0DAA0D;EACnEC,MAAM,EAAE;AACV,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}