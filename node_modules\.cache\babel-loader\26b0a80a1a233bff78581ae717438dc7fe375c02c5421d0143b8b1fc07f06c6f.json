{"ast": null, "code": "var _jsxFileName = \"D:\\\\leadcdn\\\\src\\\\components\\\\Header\\\\Header.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './Header.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  // Close menu when clicking on a link\n  const handleLinkClick = () => {\n    setIsMenuOpen(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header__content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header__logo\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo__icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"32\",\n                height: \"32\",\n                viewBox: \"0 0 32 32\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"16\",\n                  cy: \"16\",\n                  r: \"16\",\n                  fill: \"#0066cc\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M12 10h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z\",\n                  fill: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 25,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logo__text\",\n              children: \"LeadCRM\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: `header__nav ${isMenuOpen ? 'header__nav--open' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"nav__list\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav__item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#features\",\n                className: \"nav__link\",\n                onClick: handleLinkClick,\n                children: \"Features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav__item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#integrations\",\n                className: \"nav__link\",\n                onClick: handleLinkClick,\n                children: \"Integrations\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav__item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#pricing\",\n                className: \"nav__link\",\n                onClick: handleLinkClick,\n                children: \"Pricing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav__item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#resources\",\n                className: \"nav__link\",\n                onClick: handleLinkClick,\n                children: \"Resources\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"nav__item\",\n              children: /*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"#support\",\n                className: \"nav__link\",\n                onClick: handleLinkClick,\n                children: \"Support\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header__actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#login\",\n            className: \"header__login\",\n            children: \"Log in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#signup\",\n            className: \"btn btn--primary\",\n            children: \"Get Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `header__menu-toggle ${isMenuOpen ? 'header__menu-toggle--open' : ''}`,\n          onClick: toggleMenu,\n          \"aria-label\": \"Toggle menu\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Header", "_s", "isMenuOpen", "setIsMenuOpen", "toggleMenu", "handleLinkClick", "className", "children", "width", "height", "viewBox", "fill", "cx", "cy", "r", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "href", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/leadcdn/src/components/Header/Header.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport './Header.css';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  // Close menu when clicking on a link\n  const handleLinkClick = () => {\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"header\">\n      <div className=\"container\">\n        <div className=\"header__content\">\n          <div className=\"header__logo\">\n            <div className=\"logo\">\n              <div className=\"logo__icon\">\n                <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\n                  <circle cx=\"16\" cy=\"16\" r=\"16\" fill=\"#0066cc\"/>\n                  <path d=\"M12 10h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z\" fill=\"white\"/>\n                </svg>\n              </div>\n              <span className=\"logo__text\">LeadCRM</span>\n            </div>\n          </div>\n\n          <nav className={`header__nav ${isMenuOpen ? 'header__nav--open' : ''}`}>\n            <ul className=\"nav__list\">\n              <li className=\"nav__item\">\n                <a href=\"#features\" className=\"nav__link\" onClick={handleLinkClick}>Features</a>\n              </li>\n              <li className=\"nav__item\">\n                <a href=\"#integrations\" className=\"nav__link\" onClick={handleLinkClick}>Integrations</a>\n              </li>\n              <li className=\"nav__item\">\n                <a href=\"#pricing\" className=\"nav__link\" onClick={handleLinkClick}>Pricing</a>\n              </li>\n              <li className=\"nav__item\">\n                <a href=\"#resources\" className=\"nav__link\" onClick={handleLinkClick}>Resources</a>\n              </li>\n              <li className=\"nav__item\">\n                <a href=\"#support\" className=\"nav__link\" onClick={handleLinkClick}>Support</a>\n              </li>\n            </ul>\n          </nav>\n\n          <div className=\"header__actions\">\n            <a href=\"#login\" className=\"header__login\">Log in</a>\n            <a href=\"#signup\" className=\"btn btn--primary\">Get Started</a>\n          </div>\n\n          <button\n            className={`header__menu-toggle ${isMenuOpen ? 'header__menu-toggle--open' : ''}`}\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n          >\n            <span></span>\n            <span></span>\n            <span></span>\n          </button>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMO,UAAU,GAAGA,CAAA,KAAM;IACvBD,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5BF,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,oBACEJ,OAAA;IAAQO,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBR,OAAA;QAAKO,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BR,OAAA;UAAKO,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BR,OAAA;YAAKO,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBR,OAAA;cAAKO,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBR,OAAA;gBAAKS,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAJ,QAAA,gBACzDR,OAAA;kBAAQa,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACH,IAAI,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC/CnB,OAAA;kBAAMoB,CAAC,EAAC,iDAAiD;kBAACR,IAAI,EAAC;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNnB,OAAA;cAAMO,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnB,OAAA;UAAKO,SAAS,EAAE,eAAeJ,UAAU,GAAG,mBAAmB,GAAG,EAAE,EAAG;UAAAK,QAAA,eACrER,OAAA;YAAIO,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBR,OAAA;cAAIO,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBR,OAAA;gBAAGqB,IAAI,EAAC,WAAW;gBAACd,SAAS,EAAC,WAAW;gBAACe,OAAO,EAAEhB,eAAgB;gBAAAE,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eACLnB,OAAA;cAAIO,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBR,OAAA;gBAAGqB,IAAI,EAAC,eAAe;gBAACd,SAAS,EAAC,WAAW;gBAACe,OAAO,EAAEhB,eAAgB;gBAAAE,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC,eACLnB,OAAA;cAAIO,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBR,OAAA;gBAAGqB,IAAI,EAAC,UAAU;gBAACd,SAAS,EAAC,WAAW;gBAACe,OAAO,EAAEhB,eAAgB;gBAAAE,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC,eACLnB,OAAA;cAAIO,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBR,OAAA;gBAAGqB,IAAI,EAAC,YAAY;gBAACd,SAAS,EAAC,WAAW;gBAACe,OAAO,EAAEhB,eAAgB;gBAAAE,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,eACLnB,OAAA;cAAIO,SAAS,EAAC,WAAW;cAAAC,QAAA,eACvBR,OAAA;gBAAGqB,IAAI,EAAC,UAAU;gBAACd,SAAS,EAAC,WAAW;gBAACe,OAAO,EAAEhB,eAAgB;gBAAAE,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENnB,OAAA;UAAKO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BR,OAAA;YAAGqB,IAAI,EAAC,QAAQ;YAACd,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDnB,OAAA;YAAGqB,IAAI,EAAC,SAAS;YAACd,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAENnB,OAAA;UACEO,SAAS,EAAE,uBAAuBJ,UAAU,GAAG,2BAA2B,GAAG,EAAE,EAAG;UAClFmB,OAAO,EAAEjB,UAAW;UACpB,cAAW,aAAa;UAAAG,QAAA,gBAExBR,OAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnB,OAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnB,OAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACjB,EAAA,CAlEID,MAAM;AAAAsB,EAAA,GAANtB,MAAM;AAoEZ,eAAeA,MAAM;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}