{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from 'react';\nconst useScrollAnimation = (threshold = 0.1) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(false);\n  const ref = useRef();\n  useEffect(() => {\n    const observer = new IntersectionObserver(([entry]) => {\n      if (entry.isIntersecting) {\n        setIsVisible(true);\n        observer.unobserve(entry.target);\n      }\n    }, {\n      threshold\n    });\n    if (ref.current) {\n      observer.observe(ref.current);\n    }\n    return () => {\n      if (ref.current) {\n        observer.unobserve(ref.current);\n      }\n    };\n  }, [threshold]);\n  return [ref, isVisible];\n};\n_s(useScrollAnimation, \"7N8EcRPlcY6o9kzg5IgMZgWhyLI=\");\nexport default useScrollAnimation;", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useScrollAnimation", "threshold", "_s", "isVisible", "setIsVisible", "ref", "observer", "IntersectionObserver", "entry", "isIntersecting", "unobserve", "target", "current", "observe"], "sources": ["D:/leadcdn/src/hooks/useScrollAnimation.js"], "sourcesContent": ["import { useEffect, useRef, useState } from 'react';\n\nconst useScrollAnimation = (threshold = 0.1) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const ref = useRef();\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          observer.unobserve(entry.target);\n        }\n      },\n      { threshold }\n    );\n\n    if (ref.current) {\n      observer.observe(ref.current);\n    }\n\n    return () => {\n      if (ref.current) {\n        observer.unobserve(ref.current);\n      }\n    };\n  }, [threshold]);\n\n  return [ref, isVisible];\n};\n\nexport default useScrollAnimation;\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAEnD,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,GAAG,GAAG,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGL,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMM,GAAG,GAAGP,MAAM,CAAC,CAAC;EAEpBD,SAAS,CAAC,MAAM;IACd,MAAMS,QAAQ,GAAG,IAAIC,oBAAoB,CACvC,CAAC,CAACC,KAAK,CAAC,KAAK;MACX,IAAIA,KAAK,CAACC,cAAc,EAAE;QACxBL,YAAY,CAAC,IAAI,CAAC;QAClBE,QAAQ,CAACI,SAAS,CAACF,KAAK,CAACG,MAAM,CAAC;MAClC;IACF,CAAC,EACD;MAAEV;IAAU,CACd,CAAC;IAED,IAAII,GAAG,CAACO,OAAO,EAAE;MACfN,QAAQ,CAACO,OAAO,CAACR,GAAG,CAACO,OAAO,CAAC;IAC/B;IAEA,OAAO,MAAM;MACX,IAAIP,GAAG,CAACO,OAAO,EAAE;QACfN,QAAQ,CAACI,SAAS,CAACL,GAAG,CAACO,OAAO,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;EAEf,OAAO,CAACI,GAAG,EAAEF,SAAS,CAAC;AACzB,CAAC;AAACD,EAAA,CA3BIF,kBAAkB;AA6BxB,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}