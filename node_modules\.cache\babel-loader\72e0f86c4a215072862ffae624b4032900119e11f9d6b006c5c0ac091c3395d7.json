{"ast": null, "code": "var _jsxFileName = \"D:\\\\leadcdn\\\\src\\\\App.js\";\nimport React from 'react';\nimport Header from './components/Header/Header';\nimport Hero from './components/Hero/Hero';\nimport Features from './components/Features/Features';\nimport HowItWorks from './components/HowItWorks/HowItWorks';\nimport Testimonials from './components/Testimonials/Testimonials';\nimport IntegrationSolutions from './components/IntegrationSolutions/IntegrationSolutions';\nimport Support from './components/Support/Support';\nimport CTA from './components/CTA/CTA';\nimport Footer from './components/Footer/Footer';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Features, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(HowItWorks, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Testimonials, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IntegrationSolutions, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Support, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CTA, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Header", "Hero", "Features", "HowItWorks", "Testimonials", "IntegrationSolutions", "Support", "CTA", "Footer", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/leadcdn/src/App.js"], "sourcesContent": ["import React from 'react';\nimport Header from './components/Header/Header';\nimport <PERSON> from './components/Hero/Hero';\nimport Features from './components/Features/Features';\nimport HowItWorks from './components/HowItWorks/HowItWorks';\nimport Testimonials from './components/Testimonials/Testimonials';\nimport IntegrationSolutions from './components/IntegrationSolutions/IntegrationSolutions';\nimport Support from './components/Support/Support';\nimport CTA from './components/CTA/CTA';\nimport Footer from './components/Footer/Footer';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <Header />\n      <Hero />\n      <Features />\n      <HowItWorks />\n      <Testimonials />\n      <IntegrationSolutions />\n      <Support />\n      <CTA />\n      <Footer />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,wBAAwB;AACzC,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,oBAAoB,MAAM,wDAAwD;AACzF,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,GAAG,MAAM,sBAAsB;AACtC,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBH,OAAA,CAACV,MAAM;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVP,OAAA,CAACT,IAAI;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACRP,OAAA,CAACR,QAAQ;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACZP,OAAA,CAACP,UAAU;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdP,OAAA,CAACN,YAAY;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBP,OAAA,CAACL,oBAAoB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACxBP,OAAA,CAACJ,OAAO;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXP,OAAA,CAACH,GAAG;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACPP,OAAA,CAACF,MAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACC,EAAA,GAdQP,GAAG;AAgBZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}