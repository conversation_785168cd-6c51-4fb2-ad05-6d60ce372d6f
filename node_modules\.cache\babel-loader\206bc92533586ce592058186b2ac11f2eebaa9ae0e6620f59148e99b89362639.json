{"ast": null, "code": "var _jsxFileName = \"D:\\\\leadcdn\\\\src\\\\components\\\\Testimonials\\\\Testimonials.js\";\nimport React from 'react';\nimport './Testimonials.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Testimonials = () => {\n  const testimonials = [{\n    quote: \"LeadCRM's LinkedIn integration has transformed our sales process. We've seen a 300% increase in qualified leads since implementation.\",\n    author: \"<PERSON>\",\n    title: \"VP of Sales\",\n    company: \"TechCorp Inc.\",\n    avatar: \"SJ\",\n    rating: 5\n  }, {\n    quote: \"The automatic data enrichment saves our team 10+ hours per week. The integration is seamless and the data quality is exceptional.\",\n    author: \"<PERSON>\",\n    title: \"Sales Director\",\n    company: \"Growth Solutions\",\n    avatar: \"MC\",\n    rating: 5\n  }, {\n    quote: \"Finally, a LinkedIn CRM integration that actually works! The real-time sync and lead scoring features are game-changers.\",\n    author: \"<PERSON>\",\n    title: \"Marketing Manager\",\n    company: \"StartupXYZ\",\n    avatar: \"ER\",\n    rating: 5\n  }];\n  const renderStars = rating => {\n    return Array.from({\n      length: 5\n    }, (_, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `star ${index < rating ? 'star--filled' : ''}`,\n      children: \"\\u2605\"\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"testimonials section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"testimonials__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"testimonials__title\",\n          children: \"What People are Saying About LeadCRM\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"testimonials__subtitle\",\n          children: \"Join thousands of sales professionals who trust LeadCRM for their LinkedIn integration needs\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"testimonials__grid\",\n        children: testimonials.map((testimonial, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"testimonial-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"testimonial-card__rating\",\n            children: renderStars(testimonial.rating)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"blockquote\", {\n            className: \"testimonial-card__quote\",\n            children: [\"\\\"\", testimonial.quote, \"\\\"\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"testimonial-card__author\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"author__avatar\",\n              children: testimonial.avatar\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"author__info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"author__name\",\n                children: testimonial.author\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"author__title\",\n                children: testimonial.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"author__company\",\n                children: testimonial.company\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"testimonials__stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat__number\",\n            children: \"4.9/5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat__label\",\n            children: \"Average Rating\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat__number\",\n            children: \"10,000+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat__label\",\n            children: \"Happy Customers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat__number\",\n            children: \"50M+\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat__label\",\n            children: \"Contacts Enriched\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_c = Testimonials;\nexport default Testimonials;\nvar _c;\n$RefreshReg$(_c, \"Testimonials\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Testimonials", "testimonials", "quote", "author", "title", "company", "avatar", "rating", "renderStars", "Array", "from", "length", "_", "index", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "testimonial", "_c", "$RefreshReg$"], "sources": ["D:/leadcdn/src/components/Testimonials/Testimonials.js"], "sourcesContent": ["import React from 'react';\nimport './Testimonials.css';\n\nconst Testimonials = () => {\n  const testimonials = [\n    {\n      quote: \"LeadCRM's LinkedIn integration has transformed our sales process. We've seen a 300% increase in qualified leads since implementation.\",\n      author: \"<PERSON>\",\n      title: \"VP of Sales\",\n      company: \"TechCorp Inc.\",\n      avatar: \"SJ\",\n      rating: 5\n    },\n    {\n      quote: \"The automatic data enrichment saves our team 10+ hours per week. The integration is seamless and the data quality is exceptional.\",\n      author: \"<PERSON>\",\n      title: \"Sales Director\",\n      company: \"Growth Solutions\",\n      avatar: \"MC\",\n      rating: 5\n    },\n    {\n      quote: \"Finally, a LinkedIn CRM integration that actually works! The real-time sync and lead scoring features are game-changers.\",\n      author: \"<PERSON>\",\n      title: \"Marketing Manager\",\n      company: \"StartupXYZ\",\n      avatar: \"ER\",\n      rating: 5\n    }\n  ];\n\n  const renderStars = (rating) => {\n    return Array.from({ length: 5 }, (_, index) => (\n      <span key={index} className={`star ${index < rating ? 'star--filled' : ''}`}>\n        ★\n      </span>\n    ));\n  };\n\n  return (\n    <section className=\"testimonials section\">\n      <div className=\"container\">\n        <div className=\"testimonials__header\">\n          <h2 className=\"testimonials__title\">\n            What People are Saying About LeadCRM\n          </h2>\n          <p className=\"testimonials__subtitle\">\n            Join thousands of sales professionals who trust LeadCRM for their LinkedIn integration needs\n          </p>\n        </div>\n\n        <div className=\"testimonials__grid\">\n          {testimonials.map((testimonial, index) => (\n            <div key={index} className=\"testimonial-card\">\n              <div className=\"testimonial-card__rating\">\n                {renderStars(testimonial.rating)}\n              </div>\n              <blockquote className=\"testimonial-card__quote\">\n                \"{testimonial.quote}\"\n              </blockquote>\n              <div className=\"testimonial-card__author\">\n                <div className=\"author__avatar\">\n                  {testimonial.avatar}\n                </div>\n                <div className=\"author__info\">\n                  <div className=\"author__name\">{testimonial.author}</div>\n                  <div className=\"author__title\">{testimonial.title}</div>\n                  <div className=\"author__company\">{testimonial.company}</div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"testimonials__stats\">\n          <div className=\"stat\">\n            <div className=\"stat__number\">4.9/5</div>\n            <div className=\"stat__label\">Average Rating</div>\n          </div>\n          <div className=\"stat\">\n            <div className=\"stat__number\">10,000+</div>\n            <div className=\"stat__label\">Happy Customers</div>\n          </div>\n          <div className=\"stat\">\n            <div className=\"stat__number\">50M+</div>\n            <div className=\"stat__label\">Contacts Enriched</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Testimonials;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACzB,MAAMC,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,uIAAuI;IAC9IC,MAAM,EAAE,eAAe;IACvBC,KAAK,EAAE,aAAa;IACpBC,OAAO,EAAE,eAAe;IACxBC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,KAAK,EAAE,mIAAmI;IAC1IC,MAAM,EAAE,cAAc;IACtBC,KAAK,EAAE,gBAAgB;IACvBC,OAAO,EAAE,kBAAkB;IAC3BC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,KAAK,EAAE,0HAA0H;IACjIC,MAAM,EAAE,iBAAiB;IACzBC,KAAK,EAAE,mBAAmB;IAC1BC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE;EACV,CAAC,CACF;EAED,MAAMC,WAAW,GAAID,MAAM,IAAK;IAC9B,OAAOE,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,EAAE,CAACC,CAAC,EAAEC,KAAK,kBACxCd,OAAA;MAAkBe,SAAS,EAAE,QAAQD,KAAK,GAAGN,MAAM,GAAG,cAAc,GAAG,EAAE,EAAG;MAAAQ,QAAA,EAAC;IAE7E,GAFWF,KAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CACP,CAAC;EACJ,CAAC;EAED,oBACEpB,OAAA;IAASe,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACvChB,OAAA;MAAKe,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBhB,OAAA;QAAKe,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnChB,OAAA;UAAIe,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAEpC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGe,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAChCd,YAAY,CAACmB,GAAG,CAAC,CAACC,WAAW,EAAER,KAAK,kBACnCd,OAAA;UAAiBe,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC3ChB,OAAA;YAAKe,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACtCP,WAAW,CAACa,WAAW,CAACd,MAAM;UAAC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNpB,OAAA;YAAYe,SAAS,EAAC,yBAAyB;YAAAC,QAAA,GAAC,IAC7C,EAACM,WAAW,CAACnB,KAAK,EAAC,IACtB;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpB,OAAA;YAAKe,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvChB,OAAA;cAAKe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BM,WAAW,CAACf;YAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BhB,OAAA;gBAAKe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEM,WAAW,CAAClB;cAAM;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDpB,OAAA;gBAAKe,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEM,WAAW,CAACjB;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDpB,OAAA;gBAAKe,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEM,WAAW,CAAChB;cAAO;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAhBEN,KAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENpB,OAAA;QAAKe,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClChB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCpB,OAAA;YAAKe,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3CpB,OAAA;YAAKe,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNpB,OAAA;UAAKe,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBhB,OAAA;YAAKe,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxCpB,OAAA;YAAKe,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACG,EAAA,GAxFItB,YAAY;AA0FlB,eAAeA,YAAY;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}