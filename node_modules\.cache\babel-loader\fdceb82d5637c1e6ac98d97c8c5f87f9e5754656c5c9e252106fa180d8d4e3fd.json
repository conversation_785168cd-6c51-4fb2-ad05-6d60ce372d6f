{"ast": null, "code": "var _jsxFileName = \"D:\\\\leadcdn\\\\src\\\\components\\\\HowItWorks\\\\HowItWorks.js\";\nimport React from 'react';\nimport './HowItWorks.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HowItWorks = () => {\n  const steps = [{\n    number: '1',\n    title: 'Connect LinkedIn',\n    description: 'Securely connect your LinkedIn account with our OAuth 2.0 integration',\n    icon: '🔗'\n  }, {\n    number: '2',\n    title: 'Select CRM',\n    description: 'Choose your preferred CRM platform from our supported integrations',\n    icon: '⚙️'\n  }, {\n    number: '3',\n    title: 'Configure Sync',\n    description: 'Set up automatic sync rules and data mapping preferences',\n    icon: '🔄'\n  }, {\n    number: '4',\n    title: 'Start Enriching',\n    description: 'Watch as your contacts are automatically enriched with LinkedIn data',\n    icon: '✨'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"how-it-works section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"how-it-works__header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"how-it-works__title\",\n          children: \"How It Works\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"how-it-works__subtitle\",\n          children: \"Get started in minutes with our simple 4-step process\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"steps\",\n        children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"step\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step__number\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: step.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step__content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step__icon\",\n              children: step.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"step__title\",\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"step__description\",\n              children: step.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 15\n          }, this), index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step__connector\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M5 12h14m-7-7l7 7-7 7\",\n                stroke: \"#0066cc\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 56,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"how-it-works__cta\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#get-started\",\n          className: \"btn btn--primary btn--large\",\n          children: \"Start Your Free Trial\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"how-it-works__note\",\n          children: \"No credit card required \\u2022 14-day free trial \\u2022 Cancel anytime\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c = HowItWorks;\nexport default HowItWorks;\nvar _c;\n$RefreshReg$(_c, \"HowItWorks\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "HowItWorks", "steps", "number", "title", "description", "icon", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "index", "length", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "href", "_c", "$RefreshReg$"], "sources": ["D:/leadcdn/src/components/HowItWorks/HowItWorks.js"], "sourcesContent": ["import React from 'react';\nimport './HowItWorks.css';\n\nconst HowItWorks = () => {\n  const steps = [\n    {\n      number: '1',\n      title: 'Connect LinkedIn',\n      description: 'Securely connect your LinkedIn account with our OAuth 2.0 integration',\n      icon: '🔗'\n    },\n    {\n      number: '2',\n      title: 'Select CRM',\n      description: 'Choose your preferred CRM platform from our supported integrations',\n      icon: '⚙️'\n    },\n    {\n      number: '3',\n      title: 'Configure Sync',\n      description: 'Set up automatic sync rules and data mapping preferences',\n      icon: '🔄'\n    },\n    {\n      number: '4',\n      title: 'Start Enriching',\n      description: 'Watch as your contacts are automatically enriched with LinkedIn data',\n      icon: '✨'\n    }\n  ];\n\n  return (\n    <section className=\"how-it-works section\">\n      <div className=\"container\">\n        <div className=\"how-it-works__header\">\n          <h2 className=\"how-it-works__title\">How It Works</h2>\n          <p className=\"how-it-works__subtitle\">\n            Get started in minutes with our simple 4-step process\n          </p>\n        </div>\n\n        <div className=\"steps\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"step\">\n              <div className=\"step__number\">\n                <span>{step.number}</span>\n              </div>\n              <div className=\"step__content\">\n                <div className=\"step__icon\">{step.icon}</div>\n                <h3 className=\"step__title\">{step.title}</h3>\n                <p className=\"step__description\">{step.description}</p>\n              </div>\n              {index < steps.length - 1 && (\n                <div className=\"step__connector\">\n                  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M5 12h14m-7-7l7 7-7 7\" stroke=\"#0066cc\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                  </svg>\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n\n        <div className=\"how-it-works__cta\">\n          <a href=\"#get-started\" className=\"btn btn--primary btn--large\">\n            Start Your Free Trial\n          </a>\n          <p className=\"how-it-works__note\">\n            No credit card required • 14-day free trial • Cancel anytime\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HowItWorks;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,MAAMC,KAAK,GAAG,CACZ;IACEC,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,oEAAoE;IACjFC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,0DAA0D;IACvEC,IAAI,EAAE;EACR,CAAC,EACD;IACEH,MAAM,EAAE,GAAG;IACXC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,sEAAsE;IACnFC,IAAI,EAAE;EACR,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACvCR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA;QAAKO,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCR,OAAA;UAAIO,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrDZ,OAAA;UAAGO,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENZ,OAAA;QAAKO,SAAS,EAAC,OAAO;QAAAC,QAAA,EACnBN,KAAK,CAACW,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBf,OAAA;UAAiBO,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC/BR,OAAA;YAAKO,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BR,OAAA;cAAAQ,QAAA,EAAOM,IAAI,CAACX;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BR,OAAA;cAAKO,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEM,IAAI,CAACR;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CZ,OAAA;cAAIO,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEM,IAAI,CAACV;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7CZ,OAAA;cAAGO,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEM,IAAI,CAACT;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,EACLG,KAAK,GAAGb,KAAK,CAACc,MAAM,GAAG,CAAC,iBACvBhB,OAAA;YAAKO,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BR,OAAA;cAAKiB,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAZ,QAAA,eACzDR,OAAA;gBAAMqB,CAAC,EAAC,uBAAuB;gBAACC,MAAM,EAAC,SAAS;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC;cAAO;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAfOG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENZ,OAAA;QAAKO,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCR,OAAA;UAAG0B,IAAI,EAAC,cAAc;UAACnB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJZ,OAAA;UAAGO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACe,EAAA,GAvEI1B,UAAU;AAyEhB,eAAeA,UAAU;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}