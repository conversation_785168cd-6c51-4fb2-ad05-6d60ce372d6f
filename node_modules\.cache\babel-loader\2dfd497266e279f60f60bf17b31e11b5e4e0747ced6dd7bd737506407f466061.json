{"ast": null, "code": "var _jsxFileName = \"D:\\\\leadcdn\\\\src\\\\components\\\\Footer\\\\Footer.js\";\nimport React from 'react';\nimport './Footer.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer__content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer__brand\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer__logo\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"logo\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"logo__icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"32\",\n                  height: \"32\",\n                  viewBox: \"0 0 32 32\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"16\",\n                    cy: \"16\",\n                    r: \"16\",\n                    fill: \"#0066cc\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 16,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 10h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z\",\n                    fill: \"white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 17,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 15,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 14,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"logo__text\",\n                children: \"LeadCRM\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"footer__description\",\n            children: \"The most powerful LinkedIn CRM integration platform. Transform your LinkedIn network into a sales powerhouse.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer__social\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"social-link\",\n              \"aria-label\": \"LinkedIn\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"social-link\",\n              \"aria-label\": \"Twitter\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 35,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#\",\n              className: \"social-link\",\n              \"aria-label\": \"Facebook\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 20 20\",\n                fill: \"currentColor\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer__links\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer__column\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"footer__column-title\",\n              children: \"Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"footer__list\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#features\",\n                  className: \"footer__link\",\n                  children: \"Features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#integrations\",\n                  className: \"footer__link\",\n                  children: \"Integrations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#pricing\",\n                  className: \"footer__link\",\n                  children: \"Pricing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#api\",\n                  className: \"footer__link\",\n                  children: \"API\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#security\",\n                  className: \"footer__link\",\n                  children: \"Security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 54,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer__column\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"footer__column-title\",\n              children: \"Company\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"footer__list\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#about\",\n                  className: \"footer__link\",\n                  children: \"About Us\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 61,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#careers\",\n                  className: \"footer__link\",\n                  children: \"Careers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#blog\",\n                  className: \"footer__link\",\n                  children: \"Blog\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#press\",\n                  className: \"footer__link\",\n                  children: \"Press\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 64,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#contact\",\n                  className: \"footer__link\",\n                  children: \"Contact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 65,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer__column\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"footer__column-title\",\n              children: \"Resources\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"footer__list\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#help\",\n                  className: \"footer__link\",\n                  children: \"Help Center\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#docs\",\n                  className: \"footer__link\",\n                  children: \"Documentation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#guides\",\n                  className: \"footer__link\",\n                  children: \"Guides\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#webinars\",\n                  className: \"footer__link\",\n                  children: \"Webinars\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#status\",\n                  className: \"footer__link\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer__column\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"footer__column-title\",\n              children: \"Legal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"footer__list\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#privacy\",\n                  className: \"footer__link\",\n                  children: \"Privacy Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#terms\",\n                  className: \"footer__link\",\n                  children: \"Terms of Service\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#cookies\",\n                  className: \"footer__link\",\n                  children: \"Cookie Policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#gdpr\",\n                  className: \"footer__link\",\n                  children: \"GDPR\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"#compliance\",\n                  className: \"footer__link\",\n                  children: \"Compliance\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer__bottom\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer__copyright\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"\\xA9 \", currentYear, \" LeadCRM. All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"footer__badges\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"badge\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge__icon\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"SOC 2 Certified\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"badge\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge__icon\",\n              children: \"\\uD83D\\uDEE1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"GDPR Compliant\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"badge\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"badge__icon\",\n              children: \"\\u2B50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"99.9% Uptime\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "children", "width", "height", "viewBox", "fill", "cx", "cy", "r", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "href", "_c", "$RefreshReg$"], "sources": ["D:/leadcdn/src/components/Footer/Footer.js"], "sourcesContent": ["import React from 'react';\nimport './Footer.css';\n\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"footer\">\n      <div className=\"container\">\n        <div className=\"footer__content\">\n          <div className=\"footer__brand\">\n            <div className=\"footer__logo\">\n              <div className=\"logo\">\n                <div className=\"logo__icon\">\n                  <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\">\n                    <circle cx=\"16\" cy=\"16\" r=\"16\" fill=\"#0066cc\"/>\n                    <path d=\"M12 10h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z\" fill=\"white\"/>\n                  </svg>\n                </div>\n                <span className=\"logo__text\">LeadCRM</span>\n              </div>\n            </div>\n            <p className=\"footer__description\">\n              The most powerful LinkedIn CRM integration platform. \n              Transform your LinkedIn network into a sales powerhouse.\n            </p>\n            <div className=\"footer__social\">\n              <a href=\"#\" className=\"social-link\" aria-label=\"LinkedIn\">\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path d=\"M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"social-link\" aria-label=\"Twitter\">\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path d=\"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"social-link\" aria-label=\"Facebook\">\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path d=\"M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          <div className=\"footer__links\">\n            <div className=\"footer__column\">\n              <h4 className=\"footer__column-title\">Product</h4>\n              <ul className=\"footer__list\">\n                <li><a href=\"#features\" className=\"footer__link\">Features</a></li>\n                <li><a href=\"#integrations\" className=\"footer__link\">Integrations</a></li>\n                <li><a href=\"#pricing\" className=\"footer__link\">Pricing</a></li>\n                <li><a href=\"#api\" className=\"footer__link\">API</a></li>\n                <li><a href=\"#security\" className=\"footer__link\">Security</a></li>\n              </ul>\n            </div>\n\n            <div className=\"footer__column\">\n              <h4 className=\"footer__column-title\">Company</h4>\n              <ul className=\"footer__list\">\n                <li><a href=\"#about\" className=\"footer__link\">About Us</a></li>\n                <li><a href=\"#careers\" className=\"footer__link\">Careers</a></li>\n                <li><a href=\"#blog\" className=\"footer__link\">Blog</a></li>\n                <li><a href=\"#press\" className=\"footer__link\">Press</a></li>\n                <li><a href=\"#contact\" className=\"footer__link\">Contact</a></li>\n              </ul>\n            </div>\n\n            <div className=\"footer__column\">\n              <h4 className=\"footer__column-title\">Resources</h4>\n              <ul className=\"footer__list\">\n                <li><a href=\"#help\" className=\"footer__link\">Help Center</a></li>\n                <li><a href=\"#docs\" className=\"footer__link\">Documentation</a></li>\n                <li><a href=\"#guides\" className=\"footer__link\">Guides</a></li>\n                <li><a href=\"#webinars\" className=\"footer__link\">Webinars</a></li>\n                <li><a href=\"#status\" className=\"footer__link\">Status</a></li>\n              </ul>\n            </div>\n\n            <div className=\"footer__column\">\n              <h4 className=\"footer__column-title\">Legal</h4>\n              <ul className=\"footer__list\">\n                <li><a href=\"#privacy\" className=\"footer__link\">Privacy Policy</a></li>\n                <li><a href=\"#terms\" className=\"footer__link\">Terms of Service</a></li>\n                <li><a href=\"#cookies\" className=\"footer__link\">Cookie Policy</a></li>\n                <li><a href=\"#gdpr\" className=\"footer__link\">GDPR</a></li>\n                <li><a href=\"#compliance\" className=\"footer__link\">Compliance</a></li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"footer__bottom\">\n          <div className=\"footer__copyright\">\n            <p>&copy; {currentYear} LeadCRM. All rights reserved.</p>\n          </div>\n          <div className=\"footer__badges\">\n            <div className=\"badge\">\n              <span className=\"badge__icon\">🔒</span>\n              <span>SOC 2 Certified</span>\n            </div>\n            <div className=\"badge\">\n              <span className=\"badge__icon\">🛡️</span>\n              <span>GDPR Compliant</span>\n            </div>\n            <div className=\"badge\">\n              <span className=\"badge__icon\">⭐</span>\n              <span>99.9% Uptime</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA;IAAQK,SAAS,EAAC,QAAQ;IAAAC,QAAA,eACxBN,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBN,OAAA;QAAKK,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BN,OAAA;UAAKK,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BN,OAAA;YAAKK,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BN,OAAA;cAAKK,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBN,OAAA;gBAAKK,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBN,OAAA;kBAAKO,KAAK,EAAC,IAAI;kBAACC,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAJ,QAAA,gBACzDN,OAAA;oBAAQW,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACH,IAAI,EAAC;kBAAS;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC/CjB,OAAA;oBAAMkB,CAAC,EAAC,iDAAiD;oBAACR,IAAI,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjB,OAAA;gBAAMK,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNjB,OAAA;YAAGK,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAGnC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJjB,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BN,OAAA;cAAGmB,IAAI,EAAC,GAAG;cAACd,SAAS,EAAC,aAAa;cAAC,cAAW,UAAU;cAAAC,QAAA,eACvDN,OAAA;gBAAKO,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAJ,QAAA,eACjEN,OAAA;kBAAMkB,CAAC,EAAC;gBAA4b;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnc;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACJjB,OAAA;cAAGmB,IAAI,EAAC,GAAG;cAACd,SAAS,EAAC,aAAa;cAAC,cAAW,SAAS;cAAAC,QAAA,eACtDN,OAAA;gBAAKO,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAJ,QAAA,eACjEN,OAAA;kBAAMkB,CAAC,EAAC;gBAAwa;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/a;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACJjB,OAAA;cAAGmB,IAAI,EAAC,GAAG;cAACd,SAAS,EAAC,aAAa;cAAC,cAAW,UAAU;cAAAC,QAAA,eACvDN,OAAA;gBAAKO,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,cAAc;gBAAAJ,QAAA,eACjEN,OAAA;kBAAMkB,CAAC,EAAC;gBAAwQ;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/Q;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjB,OAAA;UAAKK,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BN,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BN,OAAA;cAAIK,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDjB,OAAA;cAAIK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BN,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,WAAW;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,eAAe;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,UAAU;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,MAAM;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,WAAW;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENjB,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BN,OAAA;cAAIK,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjDjB,OAAA;cAAIK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BN,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,QAAQ;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/DjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,UAAU;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,OAAO;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,QAAQ;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,UAAU;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAO;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENjB,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BN,OAAA;cAAIK,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDjB,OAAA;cAAIK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BN,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,OAAO;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,OAAO;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,SAAS;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9DjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,WAAW;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,SAAS;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENjB,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BN,OAAA;cAAIK,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAAC;YAAK;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CjB,OAAA;cAAIK,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC1BN,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,UAAU;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,QAAQ;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,UAAU;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtEjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,OAAO;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DjB,OAAA;gBAAAM,QAAA,eAAIN,OAAA;kBAAGmB,IAAI,EAAC,aAAa;kBAACd,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjB,OAAA;QAAKK,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BN,OAAA;UAAKK,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCN,OAAA;YAAAM,QAAA,GAAG,OAAO,EAACJ,WAAW,EAAC,gCAA8B;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACNjB,OAAA;UAAKK,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BN,OAAA;YAAKK,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBN,OAAA;cAAMK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCjB,OAAA;cAAAM,QAAA,EAAM;YAAe;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACNjB,OAAA;YAAKK,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBN,OAAA;cAAMK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCjB,OAAA;cAAAM,QAAA,EAAM;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACNjB,OAAA;YAAKK,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpBN,OAAA;cAAMK,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtCjB,OAAA;cAAAM,QAAA,EAAM;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACG,EAAA,GA/GInB,MAAM;AAiHZ,eAAeA,MAAM;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}